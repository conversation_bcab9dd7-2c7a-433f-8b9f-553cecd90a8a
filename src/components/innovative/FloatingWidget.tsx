import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Animated,
  Dimensions,
} from 'react-native';
import {
  PanGestureHandler,
  TapGestureHandler,
  State,
} from 'react-native-gesture-handler';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface WidgetData {
  id: string;
  type: 'notification' | 'quick-action' | 'info' | 'media' | 'social';
  title: string;
  content: React.ReactNode;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  moduleId: string;
  actions?: Array<{
    label: string;
    onPress: () => void;
    color?: string;
  }>;
  autoHide?: boolean;
  hideAfter?: number;
}

interface FloatingWidgetProps {
  widget: WidgetData;
  onDismiss: (id: string) => void;
  onExpand: (id: string) => void;
  initialPosition?: { x: number; y: number };
}

const FloatingWidget: React.FC<FloatingWidgetProps> = ({
  widget,
  onDismiss,
  onExpand,
  initialPosition = { x: screenWidth - 100, y: 200 },
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  
  const translateX = useRef(new Animated.Value(initialPosition.x)).current;
  const translateY = useRef(new Animated.Value(initialPosition.y)).current;
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const expandScale = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.spring(opacity, {
        toValue: 1,
        useNativeDriver: true,
      }),
      Animated.spring(scale, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto-hide if specified
    if (widget.autoHide && widget.hideAfter) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, widget.hideAfter);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleDismiss = () => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(scale, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss(widget.id);
    });
  };

  const handleExpand = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setIsExpanded(!isExpanded);
    
    Animated.spring(expandScale, {
      toValue: isExpanded ? 0 : 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();

    if (!isExpanded) {
      onExpand(widget.id);
    }
  };

  const onPanGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX, translationY: translateY } }],
    { useNativeDriver: true }
  );

  const onPanHandlerStateChange = (event: any) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      setIsDragging(false);
      
      // Snap to edges
      const { translationX, translationY } = event.nativeEvent;
      const finalX = translationX < screenWidth / 2 ? 20 : screenWidth - 80;
      const finalY = Math.max(50, Math.min(screenHeight - 100, translationY));

      Animated.parallel([
        Animated.spring(translateX, {
          toValue: finalX,
          useNativeDriver: true,
        }),
        Animated.spring(translateY, {
          toValue: finalY,
          useNativeDriver: true,
        }),
        Animated.spring(scale, {
          toValue: 1,
          useNativeDriver: true,
        }),
      ]).start();
    } else if (event.nativeEvent.state === State.BEGAN) {
      setIsDragging(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      Animated.spring(scale, {
        toValue: 1.1,
        useNativeDriver: true,
      }).start();
    }
  };

  const getPriorityColor = () => {
    switch (widget.priority) {
      case 'urgent': return ['#FF6B6B', '#FF8E8E'];
      case 'high': return ['#4ECDC4', '#44A08D'];
      case 'medium': return ['#45B7D1', '#96C93D'];
      case 'low': return ['#96C93D', '#C4E538'];
      default: return ['#667eea', '#764ba2'];
    }
  };

  return (
    <PanGestureHandler
      onGestureEvent={onPanGestureEvent}
      onHandlerStateChange={onPanHandlerStateChange}
    >
      <Animated.View
        style={{
          position: 'absolute',
          transform: [
            { translateX },
            { translateY },
            { scale },
          ],
          opacity,
          zIndex: 1000,
        }}
      >
        <TapGestureHandler onHandlerStateChange={handleExpand}>
          <Animated.View>
            {/* Main Widget */}
            <LinearGradient
              colors={getPriorityColor()}
              style={{
                width: 60,
                height: 60,
                borderRadius: 30,
                justifyContent: 'center',
                alignItems: 'center',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 8,
              }}
            >
              <Text style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}>
                {widget.type.charAt(0).toUpperCase()}
              </Text>
            </LinearGradient>

            {/* Expanded Content */}
            <Animated.View
              style={{
                position: 'absolute',
                top: -20,
                left: -100,
                transform: [{ scale: expandScale }],
                opacity: expandScale,
              }}
            >
              <BlurView
                intensity={80}
                style={{
                  width: 280,
                  minHeight: 120,
                  borderRadius: 20,
                  padding: 16,
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                }}
              >
                <Text style={{ 
                  color: 'white', 
                  fontSize: 16, 
                  fontWeight: 'bold',
                  marginBottom: 8,
                }}>
                  {widget.title}
                </Text>
                
                <View style={{ marginBottom: 12 }}>
                  {widget.content}
                </View>

                {widget.actions && (
                  <View style={{ 
                    flexDirection: 'row', 
                    justifyContent: 'space-around',
                    marginTop: 8,
                  }}>
                    {widget.actions.map((action, index) => (
                      <TapGestureHandler
                        key={index}
                        onHandlerStateChange={() => {
                          if (event.nativeEvent.state === State.END) {
                            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                            action.onPress();
                          }
                        }}
                      >
                        <Animated.View
                          style={{
                            backgroundColor: action.color || 'rgba(255, 255, 255, 0.2)',
                            paddingHorizontal: 12,
                            paddingVertical: 6,
                            borderRadius: 12,
                          }}
                        >
                          <Text style={{ color: 'white', fontSize: 12 }}>
                            {action.label}
                          </Text>
                        </Animated.View>
                      </TapGestureHandler>
                    ))}
                  </View>
                )}
              </BlurView>
            </Animated.View>
          </Animated.View>
        </TapGestureHandler>
      </Animated.View>
    </PanGestureHandler>
  );
};

export default FloatingWidget;
