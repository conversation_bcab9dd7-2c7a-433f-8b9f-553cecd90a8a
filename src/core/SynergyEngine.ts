/**
 * Core Synergy Engine - The heart of the Super App
 * Enables seamless data sharing and cross-module interactions
 */

export interface SynergyData {
  id: string;
  moduleId: string;
  type: string;
  data: any;
  timestamp: number;
  tags: string[];
  privacy: 'public' | 'private' | 'family' | 'work';
  expiresAt?: number;
}

export interface SynergyConnection {
  sourceModule: string;
  targetModule: string;
  dataType: string;
  transformFn?: (data: any) => any;
  conditions?: (data: any) => boolean;
}

export interface ModuleContext {
  userId: string;
  location?: { lat: number; lng: number };
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
  mood?: string;
  activity?: string;
  preferences: Record<string, any>;
}

class SynergyEngine {
  private static instance: SynergyEngine;
  private dataStore: Map<string, SynergyData> = new Map();
  private connections: SynergyConnection[] = [];
  private subscribers: Map<string, ((data: SynergyData) => void)[]> = new Map();
  private context: ModuleContext | null = null;

  private constructor() {}

  static getInstance(): SynergyEngine {
    if (!SynergyEngine.instance) {
      SynergyEngine.instance = new SynergyEngine();
    }
    return SynergyEngine.instance;
  }

  // Context Management
  setContext(context: ModuleContext) {
    this.context = context;
    this.notifyContextChange();
  }

  getContext(): ModuleContext | null {
    return this.context;
  }

  // Data Management
  shareData(data: Omit<SynergyData, 'id' | 'timestamp'>): string {
    const synergyData: SynergyData = {
      ...data,
      id: this.generateId(),
      timestamp: Date.now(),
    };

    this.dataStore.set(synergyData.id, synergyData);
    this.processConnections(synergyData);
    this.notifySubscribers(synergyData);

    return synergyData.id;
  }

  getData(id: string): SynergyData | null {
    return this.dataStore.get(id) || null;
  }

  getDataByModule(moduleId: string, type?: string): SynergyData[] {
    return Array.from(this.dataStore.values()).filter(
      data => data.moduleId === moduleId && (!type || data.type === type)
    );
  }

  getDataByTags(tags: string[]): SynergyData[] {
    return Array.from(this.dataStore.values()).filter(
      data => tags.some(tag => data.tags.includes(tag))
    );
  }

  // Connection Management
  addConnection(connection: SynergyConnection) {
    this.connections.push(connection);
  }

  removeConnection(sourceModule: string, targetModule: string, dataType: string) {
    this.connections = this.connections.filter(
      conn => !(conn.sourceModule === sourceModule && 
                conn.targetModule === targetModule && 
                conn.dataType === dataType)
    );
  }

  // Subscription Management
  subscribe(moduleId: string, callback: (data: SynergyData) => void) {
    if (!this.subscribers.has(moduleId)) {
      this.subscribers.set(moduleId, []);
    }
    this.subscribers.get(moduleId)!.push(callback);
  }

  unsubscribe(moduleId: string, callback: (data: SynergyData) => void) {
    const callbacks = this.subscribers.get(moduleId);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Smart Recommendations
  getRecommendations(moduleId: string): SynergyData[] {
    const moduleData = this.getDataByModule(moduleId);
    const recommendations: SynergyData[] = [];

    // Analyze patterns and suggest cross-module synergies
    for (const data of moduleData) {
      const relatedData = this.findRelatedData(data);
      recommendations.push(...relatedData);
    }

    return recommendations.slice(0, 10); // Limit to top 10
  }

  // Private Methods
  private processConnections(data: SynergyData) {
    const relevantConnections = this.connections.filter(
      conn => conn.sourceModule === data.moduleId && conn.dataType === data.type
    );

    for (const connection of relevantConnections) {
      if (!connection.conditions || connection.conditions(data.data)) {
        const transformedData = connection.transformFn 
          ? connection.transformFn(data.data) 
          : data.data;

        // Create new synergy data for target module
        this.shareData({
          moduleId: connection.targetModule,
          type: `synergy_${data.type}`,
          data: transformedData,
          tags: [...data.tags, 'synergy'],
          privacy: data.privacy,
        });
      }
    }
  }

  private notifySubscribers(data: SynergyData) {
    // Notify direct subscribers
    const callbacks = this.subscribers.get(data.moduleId) || [];
    callbacks.forEach(callback => callback(data));

    // Notify subscribers interested in this data type
    const typeCallbacks = this.subscribers.get(`type:${data.type}`) || [];
    typeCallbacks.forEach(callback => callback(data));
  }

  private notifyContextChange() {
    if (!this.context) return;

    // Notify all modules about context change
    for (const [moduleId, callbacks] of this.subscribers) {
      if (!moduleId.startsWith('type:')) {
        callbacks.forEach(callback => {
          callback({
            id: this.generateId(),
            moduleId: 'system',
            type: 'context_change',
            data: this.context,
            timestamp: Date.now(),
            tags: ['context', 'system'],
            privacy: 'private',
          });
        });
      }
    }
  }

  private findRelatedData(data: SynergyData): SynergyData[] {
    const related: SynergyData[] = [];
    
    // Find data with similar tags
    const tagMatches = this.getDataByTags(data.tags).filter(d => d.id !== data.id);
    related.push(...tagMatches);

    // Find data from connected modules
    const connectedModules = this.connections
      .filter(conn => conn.sourceModule === data.moduleId)
      .map(conn => conn.targetModule);

    for (const moduleId of connectedModules) {
      const moduleData = this.getDataByModule(moduleId);
      related.push(...moduleData.slice(0, 3)); // Limit per module
    }

    return related;
  }

  private generateId(): string {
    return `synergy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Cleanup expired data
  cleanup() {
    const now = Date.now();
    for (const [id, data] of this.dataStore) {
      if (data.expiresAt && data.expiresAt < now) {
        this.dataStore.delete(id);
      }
    }
  }
}

export default SynergyEngine;
