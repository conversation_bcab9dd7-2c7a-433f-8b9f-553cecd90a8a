// Core Types for SuperApp

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  preferences: UserPreferences;
  familyId?: string;
  workspaceIds: string[];
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  notifications: boolean;
  locationSharing: boolean;
  dataSync: boolean;
  privacyLevel: 'public' | 'friends' | 'family' | 'private';
  language: string;
  timezone: string;
}

export interface Location {
  lat: number;
  lng: number;
  address?: string;
  timestamp: number;
}

export interface Notification {
  id: string;
  title: string;
  body: string;
  type: 'info' | 'warning' | 'error' | 'success';
  moduleId: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  timestamp: number;
  isRead: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  id: string;
  label: string;
  action: () => void;
  style?: 'default' | 'destructive';
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface SyncStatus {
  moduleId: string;
  lastSync: number;
  status: 'synced' | 'syncing' | 'error' | 'offline';
  errorMessage?: string;
}

// Module-specific types
export * from '../modules/social/types';
export * from '../modules/entertainment/types';
export * from '../modules/family/types';
