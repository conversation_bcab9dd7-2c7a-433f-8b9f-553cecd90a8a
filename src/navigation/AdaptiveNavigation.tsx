import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import {
  PanGestureHandler,
  State,
} from 'react-native-gesture-handler';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import SynergyEngine, { ModuleContext } from '../core/SynergyEngine';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface NavigationModule {
  id: string;
  name: string;
  icon: string;
  color: string[];
  priority: number;
  contextRelevance: (context: ModuleContext) => number; // 0-1 score
  component: React.ComponentType;
  quickActions?: Array<{
    id: string;
    label: string;
    icon: string;
    action: () => void;
  }>;
}

interface AdaptiveNavigationProps {
  modules: NavigationModule[];
  onModuleSelect: (moduleId: string) => void;
  currentModule?: string;
}

const AdaptiveNavigation: React.FC<AdaptiveNavigationProps> = ({
  modules,
  onModuleSelect,
  currentModule,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [contextualModules, setContextualModules] = useState<NavigationModule[]>([]);
  const [navPosition, setNavPosition] = useState<'bottom' | 'side' | 'floating'>('bottom');
  
  const synergyEngine = SynergyEngine.getInstance();
  const expandAnimation = useRef(new Animated.Value(0)).current;
  const positionAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    updateContextualModules();
    adaptNavigationLayout();
  }, []);

  const updateContextualModules = () => {
    const context = synergyEngine.getContext();
    if (!context) {
      setContextualModules(modules);
      return;
    }

    // Score modules based on context relevance
    const scoredModules = modules.map(module => ({
      ...module,
      relevanceScore: module.contextRelevance(context),
    }));

    // Sort by relevance and priority
    const sortedModules = scoredModules.sort((a, b) => {
      const relevanceDiff = b.relevanceScore - a.relevanceScore;
      if (Math.abs(relevanceDiff) > 0.1) return relevanceDiff;
      return b.priority - a.priority;
    });

    setContextualModules(sortedModules.slice(0, 6)); // Show top 6 most relevant
  };

  const adaptNavigationLayout = () => {
    const context = synergyEngine.getContext();
    if (!context) return;

    // Adapt navigation based on context
    if (context.activity === 'driving' || context.activity === 'walking') {
      setNavPosition('floating');
    } else if (context.timeOfDay === 'night') {
      setNavPosition('side');
    } else {
      setNavPosition('bottom');
    }
  };

  const handleExpand = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setIsExpanded(!isExpanded);
    
    Animated.spring(expandAnimation, {
      toValue: isExpanded ? 0 : 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();
  };

  const handleModuleSelect = (moduleId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onModuleSelect(moduleId);
    
    // Share navigation data for synergy
    synergyEngine.shareData({
      moduleId: 'navigation',
      type: 'module_selected',
      data: { moduleId, timestamp: Date.now() },
      tags: ['navigation', 'user_action'],
      privacy: 'private',
    });
  };

  const renderBottomNavigation = () => (
    <View style={{
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 90,
      paddingBottom: 20,
    }}>
      <BlurView
        intensity={80}
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-around',
          paddingHorizontal: 20,
          paddingTop: 10,
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
        }}
      >
        {contextualModules.slice(0, 4).map((module, index) => (
          <ModuleButton
            key={module.id}
            module={module}
            isActive={currentModule === module.id}
            onPress={() => handleModuleSelect(module.id)}
            size="medium"
          />
        ))}

        {contextualModules.length > 4 && (
          <ExpandButton
            onPress={handleExpand}
            isExpanded={isExpanded}
            count={contextualModules.length - 4}
          />
        )}
      </BlurView>

      {/* Expanded modules */}
      <Animated.View
        style={{
          position: 'absolute',
          bottom: 90,
          left: 20,
          right: 20,
          transform: [{ scale: expandAnimation }],
          opacity: expandAnimation,
        }}
      >
        <BlurView
          intensity={80}
          style={{
            borderRadius: 20,
            padding: 16,
            flexDirection: 'row',
            flexWrap: 'wrap',
            justifyContent: 'space-around',
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
          }}
        >
          {contextualModules.slice(4).map((module) => (
            <ModuleButton
              key={module.id}
              module={module}
              isActive={currentModule === module.id}
              onPress={() => handleModuleSelect(module.id)}
              size="small"
            />
          ))}
        </BlurView>
      </Animated.View>
    </View>
  );

  const renderSideNavigation = () => (
    <View style={{
      position: 'absolute',
      right: 0,
      top: '20%',
      bottom: '20%',
      width: 80,
    }}>
      <BlurView
        intensity={80}
        style={{
          flex: 1,
          borderTopLeftRadius: 20,
          borderBottomLeftRadius: 20,
          paddingVertical: 20,
          alignItems: 'center',
          justifyContent: 'space-around',
        }}
      >
        {contextualModules.slice(0, 5).map((module) => (
          <ModuleButton
            key={module.id}
            module={module}
            isActive={currentModule === module.id}
            onPress={() => handleModuleSelect(module.id)}
            size="small"
          />
        ))}
      </BlurView>
    </View>
  );

  const renderFloatingNavigation = () => (
    <View style={{
      position: 'absolute',
      bottom: 100,
      right: 20,
    }}>
      <PanGestureHandler>
        <Animated.View>
          <BlurView
            intensity={80}
            style={{
              width: 60,
              height: 60,
              borderRadius: 30,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <ModuleButton
              module={contextualModules[0]}
              isActive={currentModule === contextualModules[0]?.id}
              onPress={() => handleModuleSelect(contextualModules[0]?.id)}
              size="large"
            />
          </BlurView>
        </Animated.View>
      </PanGestureHandler>
    </View>
  );

  switch (navPosition) {
    case 'side':
      return renderSideNavigation();
    case 'floating':
      return renderFloatingNavigation();
    default:
      return renderBottomNavigation();
  }
};

interface ModuleButtonProps {
  module: NavigationModule;
  isActive: boolean;
  onPress: () => void;
  size: 'small' | 'medium' | 'large';
}

const ModuleButton: React.FC<ModuleButtonProps> = ({
  module,
  isActive,
  onPress,
  size,
}) => {
  const sizeMap = {
    small: { button: 50, icon: 20, text: 10 },
    medium: { button: 60, icon: 24, text: 12 },
    large: { button: 70, icon: 28, text: 14 },
  };

  const dimensions = sizeMap[size];

  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        alignItems: 'center',
        margin: 4,
        minWidth: dimensions.button,
      }}
    >
      <LinearGradient
        colors={isActive ? module.color : ['rgba(255,255,255,0.15)', 'rgba(255,255,255,0.05)']}
        style={{
          width: dimensions.button,
          height: dimensions.button,
          borderRadius: dimensions.button / 2,
          justifyContent: 'center',
          alignItems: 'center',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: isActive ? 0.4 : 0.2,
          shadowRadius: 6,
          elevation: isActive ? 6 : 3,
          borderWidth: isActive ? 2 : 1,
          borderColor: isActive ? 'rgba(255,255,255,0.3)' : 'rgba(255,255,255,0.1)',
        }}
      >
        <Text style={{
          fontSize: dimensions.icon,
          color: isActive ? 'white' : 'rgba(255,255,255,0.8)',
        }}>
          {module.icon}
        </Text>
      </LinearGradient>

      <Text style={{
        color: isActive ? 'white' : 'rgba(255,255,255,0.7)',
        fontSize: dimensions.text,
        fontWeight: isActive ? 'bold' : 'normal',
        marginTop: 4,
        textAlign: 'center',
      }}>
        {module.name}
      </Text>
    </TouchableOpacity>
  );
};

interface ExpandButtonProps {
  onPress: () => void;
  isExpanded: boolean;
  count: number;
}

const ExpandButton: React.FC<ExpandButtonProps> = ({ onPress, isExpanded, count }) => (
  <TouchableOpacity
    onPress={onPress}
    style={{
      alignItems: 'center',
      margin: 4,
      minWidth: 60,
    }}
  >
    <LinearGradient
      colors={isExpanded ? ['#4ECDC4', '#44A08D'] : ['rgba(255,255,255,0.15)', 'rgba(255,255,255,0.05)']}
      style={{
        width: 60,
        height: 60,
        borderRadius: 30,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 6,
        elevation: 3,
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.1)',
      }}
    >
      <Text style={{
        fontSize: 24,
        color: isExpanded ? 'white' : 'rgba(255,255,255,0.8)',
      }}>
        {isExpanded ? '×' : '⋯'}
      </Text>
    </LinearGradient>

    <Text style={{
      color: 'rgba(255,255,255,0.7)',
      fontSize: 12,
      fontWeight: 'normal',
      marginTop: 4,
      textAlign: 'center',
    }}>
      {isExpanded ? 'Less' : `+${count}`}
    </Text>
  </TouchableOpacity>
);

export default AdaptiveNavigation;
