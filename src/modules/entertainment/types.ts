// Entertainment Module Types

export interface MediaContent {
  id: string;
  title: string;
  description: string;
  type: 'movie' | 'tv_show' | 'music' | 'podcast' | 'game' | 'book' | 'article';
  platform: string;
  url?: string;
  thumbnail?: string;
  duration?: number; // in seconds
  releaseDate: Date;
  rating: number; // 0-5
  genre: string[];
  mood: string[];
  language: string;
  isLive?: boolean;
  isPremium?: boolean;
}

export interface Playlist {
  id: string;
  name: string;
  description?: string;
  type: 'music' | 'video' | 'podcast' | 'mixed';
  items: MediaContent[];
  isPublic: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
  thumbnail?: string;
}

export interface WatchHistory {
  id: string;
  contentId: string;
  userId: string;
  platform: string;
  watchedAt: Date;
  progress: number; // 0-1 (percentage watched)
  completed: boolean;
  rating?: number;
  review?: string;
}

export interface Recommendation {
  id: string;
  contentId: string;
  userId: string;
  score: number; // 0-1 confidence score
  reason: string;
  type: 'trending' | 'similar' | 'mood' | 'social' | 'contextual';
  metadata: {
    basedOn?: string[]; // content IDs this is based on
    mood?: string;
    context?: string;
    socialSignals?: number;
  };
  createdAt: Date;
  expiresAt?: Date;
}

export interface ContentProvider {
  id: string;
  name: string;
  type: 'streaming' | 'music' | 'podcast' | 'gaming' | 'reading';
  apiEndpoint?: string;
  isConnected: boolean;
  subscription?: {
    type: 'free' | 'premium';
    expiresAt?: Date;
  };
  features: string[];
  logo?: string;
}

export interface UserPreferences {
  favoriteGenres: string[];
  preferredLanguages: string[];
  contentTypes: string[];
  platforms: string[];
  qualityPreference: 'auto' | 'low' | 'medium' | 'high' | 'ultra';
  autoplay: boolean;
  downloadForOffline: boolean;
  parentalControls?: {
    enabled: boolean;
    maxRating: string;
    blockedGenres: string[];
    timeRestrictions: {
      start: string; // HH:MM
      end: string; // HH:MM
    };
  };
}

export interface ContentInteraction {
  id: string;
  contentId: string;
  userId: string;
  type: 'view' | 'like' | 'dislike' | 'share' | 'save' | 'rate' | 'comment';
  value?: any; // rating value, comment text, etc.
  timestamp: Date;
  platform: string;
  context?: {
    device: string;
    location?: string;
    mood?: string;
    activity?: string;
  };
}
