import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  Dimensions,
  Animated,
  TouchableOpacity,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import SynergyEngine from '../../core/SynergyEngine';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface ContentItem {
  id: string;
  title: string;
  type: 'video' | 'music' | 'podcast' | 'game' | 'book' | 'article';
  platform: string;
  thumbnail?: string;
  duration?: number;
  rating: number;
  genre: string[];
  mood: string[];
  description: string;
  isLive?: boolean;
  viewCount?: number;
  releaseDate: Date;
}

export interface PersonalizedFeed {
  id: string;
  name: string;
  description: string;
  items: ContentItem[];
  refreshRate: number; // minutes
  priority: number;
}

const EntertainmentSpectrum: React.FC = () => {
  const [feeds, setFeeds] = useState<PersonalizedFeed[]>([]);
  const [selectedFeed, setSelectedFeed] = useState<string>('discover');
  const [currentContent, setCurrentContent] = useState<ContentItem | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'immersive'>('grid');
  
  const synergyEngine = SynergyEngine.getInstance();
  const scrollAnimation = useRef(new Animated.Value(0)).current;
  const contentAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    initializeFeeds();
    setupSynergyConnections();
    generatePersonalizedContent();
  }, []);

  const initializeFeeds = () => {
    const initialFeeds: PersonalizedFeed[] = [
      {
        id: 'discover',
        name: 'Discover',
        description: 'AI-curated content based on your mood and context',
        items: [],
        refreshRate: 15,
        priority: 1,
      },
      {
        id: 'trending',
        name: 'Trending Now',
        description: 'What\'s hot across all platforms',
        items: [],
        refreshRate: 30,
        priority: 2,
      },
      {
        id: 'continue',
        name: 'Continue Watching',
        description: 'Pick up where you left off',
        items: [],
        refreshRate: 60,
        priority: 3,
      },
      {
        id: 'social',
        name: 'From Friends',
        description: 'Content shared by your social circle',
        items: [],
        refreshRate: 45,
        priority: 4,
      },
      {
        id: 'mood',
        name: 'Mood Match',
        description: 'Content that matches your current vibe',
        items: [],
        refreshRate: 20,
        priority: 5,
      },
    ];

    setFeeds(initialFeeds);
  };

  const setupSynergyConnections = () => {
    // Connect with social module for friend recommendations
    synergyEngine.addConnection({
      sourceModule: 'social',
      targetModule: 'entertainment',
      dataType: 'shared_content',
      transformFn: (data) => ({
        type: 'social_recommendation',
        content: data.content,
        sharedBy: data.sender,
        platform: data.platform,
      }),
    });

    // Connect with work module for break-time content
    synergyEngine.addConnection({
      sourceModule: 'work',
      targetModule: 'entertainment',
      dataType: 'break_time',
      transformFn: (data) => ({
        type: 'quick_entertainment',
        duration: data.breakDuration,
        preference: 'short_form',
      }),
    });

    // Connect with family module for family-friendly content
    synergyEngine.addConnection({
      sourceModule: 'family',
      targetModule: 'entertainment',
      dataType: 'family_activity',
      transformFn: (data) => ({
        type: 'family_content',
        ageGroup: data.ageGroup,
        participants: data.participants,
      }),
    });

    // Subscribe to context changes
    synergyEngine.subscribe('entertainment', (data) => {
      if (data.type === 'context_change') {
        adaptContentToContext(data.data);
      }
    });
  };

  const generatePersonalizedContent = () => {
    // Mock content generation - in real app, this would use AI/ML
    const mockContent: ContentItem[] = [
      {
        id: 'video_1',
        title: 'The Future of Technology',
        type: 'video',
        platform: 'YouTube',
        duration: 1200,
        rating: 4.8,
        genre: ['Technology', 'Education'],
        mood: ['Curious', 'Focused'],
        description: 'Exploring emerging technologies that will shape our future',
        viewCount: 1500000,
        releaseDate: new Date('2024-01-15'),
      },
      {
        id: 'music_1',
        title: 'Chill Vibes Playlist',
        type: 'music',
        platform: 'Spotify',
        duration: 3600,
        rating: 4.6,
        genre: ['Chill', 'Electronic'],
        mood: ['Relaxed', 'Peaceful'],
        description: 'Perfect background music for work or relaxation',
        releaseDate: new Date('2024-01-10'),
      },
      {
        id: 'podcast_1',
        title: 'Tech Talk Weekly',
        type: 'podcast',
        platform: 'Apple Podcasts',
        duration: 2700,
        rating: 4.7,
        genre: ['Technology', 'Business'],
        mood: ['Informative', 'Engaging'],
        description: 'Weekly discussions on the latest in tech',
        releaseDate: new Date('2024-01-20'),
      },
    ];

    // Distribute content across feeds based on context and preferences
    const updatedFeeds = feeds.map(feed => {
      switch (feed.id) {
        case 'discover':
          return { ...feed, items: mockContent };
        case 'trending':
          return { ...feed, items: mockContent.filter(item => item.viewCount && item.viewCount > 1000000) };
        case 'mood':
          return { ...feed, items: filterByMood(mockContent) };
        default:
          return feed;
      }
    });

    setFeeds(updatedFeeds);
  };

  const adaptContentToContext = (context: any) => {
    if (context.activity === 'commuting') {
      // Prioritize audio content
      setSelectedFeed('mood');
      setViewMode('list');
    } else if (context.timeOfDay === 'evening') {
      // Show relaxing content
      generateMoodBasedContent('relaxed');
    } else if (context.location?.type === 'gym') {
      // Show energetic content
      generateMoodBasedContent('energetic');
    }
  };

  const filterByMood = (content: ContentItem[]): ContentItem[] => {
    const context = synergyEngine.getContext();
    if (!context?.mood) return content;

    return content.filter(item => 
      item.mood.some(mood => 
        mood.toLowerCase().includes(context.mood.toLowerCase())
      )
    );
  };

  const generateMoodBasedContent = (targetMood: string) => {
    // Generate content based on specific mood
    const moodContent = feeds
      .find(f => f.id === 'discover')?.items
      .filter(item => item.mood.some(mood => mood.toLowerCase().includes(targetMood)));

    if (moodContent) {
      const moodFeed = feeds.find(f => f.id === 'mood');
      if (moodFeed) {
        moodFeed.items = moodContent;
        setFeeds([...feeds]);
      }
    }
  };

  const handleContentSelect = (content: ContentItem) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setCurrentContent(content);

    // Share content interaction for synergy
    synergyEngine.shareData({
      moduleId: 'entertainment',
      type: 'content_selected',
      data: {
        contentId: content.id,
        contentType: content.type,
        platform: content.platform,
        genre: content.genre,
        mood: content.mood,
        timestamp: Date.now(),
      },
      tags: ['entertainment', 'content', 'user_preference'],
      privacy: 'private',
    });

    // Animate content view
    Animated.spring(contentAnimation, {
      toValue: 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();
  };

  const renderFeedSelector = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={{ maxHeight: 60 }}
      contentContainerStyle={{ paddingHorizontal: 16 }}
    >
      {feeds.map((feed) => (
        <TouchableOpacity
          key={feed.id}
          onPress={() => {
            setSelectedFeed(feed.id);
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
          style={{ marginRight: 12 }}
        >
          <LinearGradient
            colors={selectedFeed === feed.id 
              ? ['#FF6B6B', '#FF8E8E'] 
              : ['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']
            }
            style={{
              paddingHorizontal: 20,
              paddingVertical: 10,
              borderRadius: 20,
              minWidth: 100,
              alignItems: 'center',
            }}
          >
            <Text style={{
              color: 'white',
              fontWeight: selectedFeed === feed.id ? 'bold' : 'normal',
              fontSize: 14,
            }}>
              {feed.name}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderContentGrid = () => {
    const currentFeed = feeds.find(f => f.id === selectedFeed);
    if (!currentFeed) return null;

    return (
      <ScrollView style={{ flex: 1, padding: 16 }}>
        <Text style={{
          color: 'white',
          fontSize: 18,
          fontWeight: 'bold',
          marginBottom: 8,
        }}>
          {currentFeed.name}
        </Text>
        
        <Text style={{
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: 14,
          marginBottom: 16,
        }}>
          {currentFeed.description}
        </Text>

        <View style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
        }}>
          {currentFeed.items.map((item) => (
            <TouchableOpacity
              key={item.id}
              onPress={() => handleContentSelect(item)}
              style={{
                width: (screenWidth - 48) / 2,
                marginBottom: 16,
              }}
            >
              <BlurView
                intensity={20}
                style={{
                  borderRadius: 16,
                  overflow: 'hidden',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                }}
              >
                {/* Thumbnail placeholder */}
                <LinearGradient
                  colors={getContentColors(item.type)}
                  style={{
                    height: 120,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Text style={{ color: 'white', fontSize: 24 }}>
                    {getContentIcon(item.type)}
                  </Text>
                  {item.isLive && (
                    <View style={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      backgroundColor: '#FF5722',
                      paddingHorizontal: 6,
                      paddingVertical: 2,
                      borderRadius: 4,
                    }}>
                      <Text style={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>
                        LIVE
                      </Text>
                    </View>
                  )}
                </LinearGradient>

                <View style={{ padding: 12 }}>
                  <Text style={{
                    color: 'white',
                    fontSize: 14,
                    fontWeight: 'bold',
                    marginBottom: 4,
                  }} numberOfLines={2}>
                    {item.title}
                  </Text>
                  
                  <Text style={{
                    color: 'rgba(255, 255, 255, 0.6)',
                    fontSize: 12,
                    marginBottom: 4,
                  }}>
                    {item.platform}
                  </Text>

                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{
                      color: 'rgba(255, 255, 255, 0.6)',
                      fontSize: 10,
                    }}>
                      {formatDuration(item.duration)}
                    </Text>
                    
                    <Text style={{
                      color: '#FFD700',
                      fontSize: 10,
                    }}>
                      ★ {item.rating}
                    </Text>
                  </View>
                </View>
              </BlurView>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    );
  };

  const getContentColors = (type: string): string[] => {
    switch (type) {
      case 'video': return ['#FF6B6B', '#FF8E8E'];
      case 'music': return ['#4ECDC4', '#44A08D'];
      case 'podcast': return ['#45B7D1', '#96C93D'];
      case 'game': return ['#A8E6CF', '#7FCDCD'];
      case 'book': return ['#FFB6C1', '#FFA0AC'];
      default: return ['#667eea', '#764ba2'];
    }
  };

  const getContentIcon = (type: string): string => {
    switch (type) {
      case 'video': return '▶';
      case 'music': return '♪';
      case 'podcast': return '🎙';
      case 'game': return '🎮';
      case 'book': return '📖';
      default: return '📱';
    }
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '';
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  const renderHeader = () => (
    <BlurView
      intensity={80}
      style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(255, 255, 255, 0.1)',
        backgroundColor: 'rgba(0, 0, 0, 0.2)',
      }}
    >
      <Text style={{
        color: 'white',
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
      }}>
        Entertainment
      </Text>
      <Text style={{
        color: 'rgba(255, 255, 255, 0.7)',
        fontSize: 14,
        textAlign: 'center',
        marginTop: 4,
      }}>
        Discover content across all platforms
      </Text>
    </BlurView>
  );

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      style={{ flex: 1 }}
    >
      {renderHeader()}
      <View style={{ paddingVertical: 16 }}>
        {renderFeedSelector()}
      </View>

      <View style={{ flex: 1 }}>
        {renderContentGrid()}
      </View>
    </LinearGradient>
  );
};

export default EntertainmentSpectrum;
