// Social Module Types

export interface SocialProfile {
  id: string;
  platform: string;
  username: string;
  displayName: string;
  avatar?: string;
  isConnected: boolean;
  lastSync: number;
}

export interface Contact {
  id: string;
  name: string;
  avatar?: string;
  platforms: SocialProfile[];
  relationship: 'family' | 'friend' | 'colleague' | 'acquaintance';
  tags: string[];
  lastInteraction: number;
}

export interface Conversation {
  id: string;
  participants: string[];
  platform: string;
  type: 'direct' | 'group';
  name?: string;
  avatar?: string;
  lastMessage?: Message;
  unreadCount: number;
  isMuted: boolean;
  isPinned: boolean;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'location' | 'sticker';
  timestamp: number;
  isRead: boolean;
  isEdited: boolean;
  replyTo?: string;
  reactions: MessageReaction[];
  attachments: MessageAttachment[];
}

export interface MessageReaction {
  emoji: string;
  users: string[];
  timestamp: number;
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'video' | 'audio' | 'file';
  url: string;
  name: string;
  size: number;
  mimeType: string;
}

export interface SocialPost {
  id: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  platform: string;
  content: string;
  media: PostMedia[];
  timestamp: number;
  likes: number;
  comments: number;
  shares: number;
  isLiked: boolean;
  isShared: boolean;
  tags: string[];
  location?: string;
}

export interface PostMedia {
  id: string;
  type: 'image' | 'video' | 'gif';
  url: string;
  thumbnail?: string;
  duration?: number;
  dimensions?: { width: number; height: number };
}

export interface SocialActivity {
  id: string;
  userId: string;
  type: 'post' | 'like' | 'comment' | 'share' | 'follow' | 'message';
  platform: string;
  targetId: string;
  timestamp: number;
  metadata: Record<string, any>;
}
