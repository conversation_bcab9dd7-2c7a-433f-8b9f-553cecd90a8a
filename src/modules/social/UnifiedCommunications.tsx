import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import SynergyEngine from '../../core/SynergyEngine';

export interface CommunicationChannel {
  id: string;
  type: 'sms' | 'whatsapp' | 'telegram' | 'discord' | 'slack' | 'email' | 'video' | 'voice';
  name: string;
  avatar?: string;
  lastMessage?: string;
  timestamp?: number;
  unreadCount: number;
  isOnline?: boolean;
  platform: string;
}

export interface Message {
  id: string;
  channelId: string;
  senderId: string;
  senderName: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'location';
  timestamp: number;
  isRead: boolean;
  reactions?: Array<{ emoji: string; users: string[] }>;
}

const UnifiedCommunications: React.FC = () => {
  const [channels, setChannels] = useState<CommunicationChannel[]>([]);
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const synergyEngine = SynergyEngine.getInstance();
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    initializeCommunications();
    setupSynergyConnections();
  }, []);

  const initializeCommunications = () => {
    // Mock data - in real app, this would connect to various APIs
    const mockChannels: CommunicationChannel[] = [
      {
        id: 'whatsapp_family',
        type: 'whatsapp',
        name: 'Family Group',
        lastMessage: 'Dinner at 7?',
        timestamp: Date.now() - 300000,
        unreadCount: 2,
        isOnline: true,
        platform: 'WhatsApp',
      },
      {
        id: 'slack_work',
        type: 'slack',
        name: 'Work Team',
        lastMessage: 'Meeting in 10 mins',
        timestamp: Date.now() - 600000,
        unreadCount: 5,
        isOnline: true,
        platform: 'Slack',
      },
      {
        id: 'discord_gaming',
        type: 'discord',
        name: 'Gaming Squad',
        lastMessage: 'Ready for raid?',
        timestamp: Date.now() - 900000,
        unreadCount: 0,
        isOnline: false,
        platform: 'Discord',
      },
    ];

    setChannels(mockChannels);
  };

  const setupSynergyConnections = () => {
    // Connect communications with other modules
    synergyEngine.addConnection({
      sourceModule: 'social',
      targetModule: 'family',
      dataType: 'message',
      transformFn: (data) => ({
        type: 'family_communication',
        content: data.content,
        sender: data.senderName,
        timestamp: data.timestamp,
      }),
      conditions: (data) => data.channelType === 'family',
    });

    synergyEngine.addConnection({
      sourceModule: 'social',
      targetModule: 'work',
      dataType: 'message',
      transformFn: (data) => ({
        type: 'work_communication',
        content: data.content,
        sender: data.senderName,
        urgency: data.content.includes('urgent') ? 'high' : 'normal',
      }),
      conditions: (data) => data.channelType === 'work',
    });

    // Subscribe to context changes
    synergyEngine.subscribe('social', (data) => {
      if (data.type === 'context_change') {
        adaptToContext(data.data);
      }
    });
  };

  const adaptToContext = (context: any) => {
    // Adapt UI based on context
    if (context.timeOfDay === 'night') {
      // Reduce notifications, show only urgent
      filterUrgentChannels();
    }
  };

  const filterUrgentChannels = () => {
    const urgentChannels = channels.filter(
      channel => channel.unreadCount > 0 || channel.type === 'voice'
    );
    setChannels(urgentChannels);
  };

  const handleChannelSelect = (channelId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedChannel(channelId);
    
    // Share communication activity
    synergyEngine.shareData({
      moduleId: 'social',
      type: 'channel_opened',
      data: { channelId, timestamp: Date.now() },
      tags: ['communication', 'user_activity'],
      privacy: 'private',
    });
  };

  const handleSendMessage = () => {
    if (!inputText.trim() || !selectedChannel) return;

    const newMessage: Message = {
      id: `msg_${Date.now()}`,
      channelId: selectedChannel,
      senderId: 'user',
      senderName: 'You',
      content: inputText,
      type: 'text',
      timestamp: Date.now(),
      isRead: true,
    };

    setMessages(prev => [...prev, newMessage]);
    setInputText('');

    // Share message data for synergy
    synergyEngine.shareData({
      moduleId: 'social',
      type: 'message',
      data: {
        content: inputText,
        channelId: selectedChannel,
        channelType: getChannelType(selectedChannel),
        timestamp: Date.now(),
      },
      tags: ['message', 'communication'],
      privacy: 'private',
    });

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getChannelType = (channelId: string): string => {
    const channel = channels.find(c => c.id === channelId);
    if (channel?.name.toLowerCase().includes('family')) return 'family';
    if (channel?.name.toLowerCase().includes('work')) return 'work';
    return 'social';
  };

  const renderChannelList = () => (
    <ScrollView style={{ flex: 1 }}>
      {channels.map((channel) => (
        <TouchableOpacity
          key={channel.id}
          onPress={() => handleChannelSelect(channel.id)}
          style={{ marginBottom: 8 }}
        >
          <BlurView
            intensity={20}
            style={{
              marginHorizontal: 16,
              borderRadius: 16,
              padding: 16,
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            }}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <LinearGradient
                colors={getChannelColors(channel.type)}
                style={{
                  width: 50,
                  height: 50,
                  borderRadius: 25,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12,
                }}
              >
                <Text style={{ color: 'white', fontWeight: 'bold' }}>
                  {channel.name.charAt(0)}
                </Text>
              </LinearGradient>

              <View style={{ flex: 1 }}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
                    {channel.name}
                  </Text>
                  <Text style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: 12 }}>
                    {channel.platform}
                  </Text>
                </View>
                
                <Text style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: 14 }}>
                  {channel.lastMessage}
                </Text>
                
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 4 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={{
                      width: 8,
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: channel.isOnline ? '#4CAF50' : '#757575',
                      marginRight: 4,
                    }} />
                    <Text style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: 12 }}>
                      {channel.isOnline ? 'Online' : 'Offline'}
                    </Text>
                  </View>
                  
                  {channel.unreadCount > 0 && (
                    <View style={{
                      backgroundColor: '#FF5722',
                      borderRadius: 10,
                      paddingHorizontal: 6,
                      paddingVertical: 2,
                    }}>
                      <Text style={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>
                        {channel.unreadCount}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </View>
          </BlurView>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderChatView = () => {
    const channel = channels.find(c => c.id === selectedChannel);
    if (!channel) return null;

    return (
      <View style={{ flex: 1 }}>
        {/* Chat Header */}
        <BlurView
          intensity={80}
          style={{
            paddingHorizontal: 16,
            paddingVertical: 12,
            borderBottomWidth: 1,
            borderBottomColor: 'rgba(255, 255, 255, 0.1)',
            backgroundColor: 'rgba(0, 0, 0, 0.2)',
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TouchableOpacity
              onPress={() => {
                setSelectedChannel(null);
              }}
              style={{ marginRight: 12 }}
            >
              <Text style={{ color: 'white', fontSize: 18 }}>←</Text>
            </TouchableOpacity>

            <Text style={{ color: 'white', fontSize: 18, fontWeight: 'bold' }}>
              {channel.name}
            </Text>
          </View>
        </BlurView>

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={{ flex: 1, padding: 16 }}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd()}
        >
          {messages
            .filter(msg => msg.channelId === selectedChannel)
            .map((message) => (
              <View
                key={message.id}
                style={{
                  alignSelf: message.senderId === 'user' ? 'flex-end' : 'flex-start',
                  marginBottom: 8,
                  maxWidth: '80%',
                }}
              >
                <BlurView
                  intensity={20}
                  style={{
                    padding: 12,
                    borderRadius: 16,
                    backgroundColor: message.senderId === 'user' 
                      ? 'rgba(33, 150, 243, 0.3)' 
                      : 'rgba(255, 255, 255, 0.1)',
                  }}
                >
                  <Text style={{ color: 'white' }}>{message.content}</Text>
                </BlurView>
              </View>
            ))}
        </ScrollView>

        {/* Input */}
        <BlurView
          intensity={80}
          style={{
            flexDirection: 'row',
            padding: 16,
            alignItems: 'center',
            borderTopWidth: 1,
            borderTopColor: 'rgba(255, 255, 255, 0.1)',
          }}
        >
          <TextInput
            style={{
              flex: 1,
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: 20,
              paddingHorizontal: 16,
              paddingVertical: 8,
              color: 'white',
              marginRight: 8,
            }}
            placeholder="Type a message..."
            placeholderTextColor="rgba(255, 255, 255, 0.5)"
            value={inputText}
            onChangeText={setInputText}
            multiline
          />
          
          <TouchableOpacity
            onPress={handleSendMessage}
            style={{
              backgroundColor: '#2196F3',
              borderRadius: 20,
              padding: 8,
            }}
          >
            <Text style={{ color: 'white', fontWeight: 'bold' }}>Send</Text>
          </TouchableOpacity>
        </BlurView>
      </View>
    );
  };

  const getChannelColors = (type: string): [string, string] => {
    switch (type) {
      case 'whatsapp': return ['#25D366', '#128C7E'];
      case 'slack': return ['#4A154B', '#611f69'];
      case 'discord': return ['#5865F2', '#7289DA'];
      case 'telegram': return ['#0088CC', '#229ED9'];
      default: return ['#667eea', '#764ba2'];
    }
  };

  const renderHeader = () => (
    <BlurView
      intensity={80}
      style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(255, 255, 255, 0.1)',
        backgroundColor: 'rgba(0, 0, 0, 0.2)',
      }}
    >
      <Text style={{
        color: 'white',
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
      }}>
        {selectedChannel ? 'Chat' : 'Communications'}
      </Text>
      {!selectedChannel && (
        <Text style={{
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: 14,
          textAlign: 'center',
          marginTop: 4,
        }}>
          All your conversations in one place
        </Text>
      )}
    </BlurView>
  );

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      style={{ flex: 1 }}
    >
      {renderHeader()}
      <View style={{ flex: 1 }}>
        {selectedChannel ? renderChatView() : renderChannelList()}
      </View>
    </LinearGradient>
  );
};

export default UnifiedCommunications;
