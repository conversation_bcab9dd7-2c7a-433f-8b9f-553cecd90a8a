// Family Module Types

export interface Family {
  id: string;
  name: string;
  members: FamilyMember[];
  settings: FamilySettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface FamilyMember {
  id: string;
  userId: string;
  familyId: string;
  role: 'parent' | 'child' | 'guardian' | 'other';
  relationship: string; // "mother", "father", "son", "daughter", etc.
  permissions: FamilyPermissions;
  joinedAt: Date;
  isActive: boolean;
}

export interface FamilyPermissions {
  canViewLocation: boolean;
  canViewCalendar: boolean;
  canViewExpenses: boolean;
  canManageEvents: boolean;
  canInviteMembers: boolean;
  canModifySettings: boolean;
  emergencyContact: boolean;
}

export interface FamilySettings {
  locationSharing: 'all' | 'parents_only' | 'none';
  calendarSharing: boolean;
  expenseTracking: boolean;
  emergencyAlerts: boolean;
  childSafetyMode: boolean;
  dataRetention: number; // days
  notifications: {
    locationUpdates: boolean;
    eventReminders: boolean;
    emergencyAlerts: boolean;
    expenseAlerts: boolean;
  };
}

export interface FamilyEvent {
  id: string;
  familyId: string;
  title: string;
  description?: string;
  type: 'birthday' | 'anniversary' | 'appointment' | 'gathering' | 'milestone' | 'emergency' | 'other';
  startDate: Date;
  endDate?: Date;
  location?: string;
  participants: string[]; // member IDs
  reminders: EventReminder[];
  isRecurring: boolean;
  recurrenceRule?: string; // RRULE format
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface EventReminder {
  id: string;
  eventId: string;
  type: 'notification' | 'email' | 'sms';
  minutesBefore: number;
  isActive: boolean;
}

export interface FamilyExpense {
  id: string;
  familyId: string;
  title: string;
  amount: number;
  currency: string;
  category: string;
  paidBy: string; // member ID
  beneficiaries: string[]; // member IDs
  splitType: 'equal' | 'custom' | 'percentage';
  splits?: ExpenseSplit[];
  date: Date;
  description?: string;
  receipt?: string; // image URL
  isRecurring: boolean;
  recurrenceRule?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ExpenseSplit {
  memberId: string;
  amount: number;
  percentage?: number;
  isPaid: boolean;
  paidAt?: Date;
}

export interface FamilyMemory {
  id: string;
  familyId: string;
  title: string;
  description?: string;
  type: 'photo' | 'video' | 'note' | 'milestone';
  content: MemoryContent[];
  date: Date;
  location?: string;
  participants: string[]; // member IDs
  tags: string[];
  isPrivate: boolean;
  createdBy: string;
  createdAt: Date;
  reactions: MemoryReaction[];
}

export interface MemoryContent {
  id: string;
  type: 'image' | 'video' | 'text';
  url?: string;
  text?: string;
  thumbnail?: string;
  duration?: number;
  order: number;
}

export interface MemoryReaction {
  id: string;
  memoryId: string;
  memberId: string;
  emoji: string;
  timestamp: Date;
}

export interface LocationUpdate {
  id: string;
  memberId: string;
  familyId: string;
  latitude: number;
  longitude: number;
  address?: string;
  accuracy: number;
  timestamp: Date;
  isManual: boolean;
  context?: {
    activity?: string;
    battery?: number;
    speed?: number;
  };
}

export interface EmergencyContact {
  id: string;
  familyId: string;
  name: string;
  relationship: string;
  phone: string;
  email?: string;
  address?: string;
  isActive: boolean;
  priority: number;
  createdAt: Date;
  updatedAt: Date;
}
