import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import * as Location from 'expo-location';
import SynergyEngine from '../../core/SynergyEngine';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface FamilyMember {
  id: string;
  name: string;
  relationship: 'parent' | 'child' | 'sibling' | 'spouse' | 'grandparent' | 'other';
  avatar?: string;
  age?: number;
  location?: {
    lat: number;
    lng: number;
    address: string;
    timestamp: number;
  };
  status: 'online' | 'busy' | 'away' | 'offline';
  mood?: string;
  currentActivity?: string;
  preferences: {
    shareLocation: boolean;
    shareActivity: boolean;
    emergencyContact: boolean;
  };
}

export interface FamilyEvent {
  id: string;
  title: string;
  type: 'birthday' | 'anniversary' | 'appointment' | 'gathering' | 'milestone' | 'emergency';
  date: Date;
  participants: string[];
  location?: string;
  description?: string;
  reminders: number[]; // minutes before event
  isRecurring: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface SharedResource {
  id: string;
  type: 'photo' | 'video' | 'document' | 'location' | 'expense' | 'memory';
  title: string;
  content: any;
  sharedBy: string;
  sharedWith: string[];
  timestamp: number;
  tags: string[];
}

const FamilyHub: React.FC = () => {
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([]);
  const [upcomingEvents, setUpcomingEvents] = useState<FamilyEvent[]>([]);
  const [sharedResources, setSharedResources] = useState<SharedResource[]>([]);
  const [selectedView, setSelectedView] = useState<'overview' | 'members' | 'events' | 'shared'>('overview');
  
  const synergyEngine = SynergyEngine.getInstance();
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const slideAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    initializeFamilyData();
    setupSynergyConnections();
    startLocationTracking();
    startPulseAnimation();
  }, []);

  const initializeFamilyData = () => {
    // Mock family data
    const mockMembers: FamilyMember[] = [
      {
        id: 'mom',
        name: 'Mom',
        relationship: 'parent',
        age: 45,
        status: 'online',
        mood: 'happy',
        currentActivity: 'cooking',
        preferences: {
          shareLocation: true,
          shareActivity: true,
          emergencyContact: true,
        },
      },
      {
        id: 'dad',
        name: 'Dad',
        relationship: 'parent',
        age: 47,
        status: 'busy',
        currentActivity: 'meeting',
        preferences: {
          shareLocation: true,
          shareActivity: false,
          emergencyContact: true,
        },
      },
      {
        id: 'sister',
        name: 'Sarah',
        relationship: 'sibling',
        age: 22,
        status: 'away',
        currentActivity: 'studying',
        preferences: {
          shareLocation: false,
          shareActivity: true,
          emergencyContact: true,
        },
      },
    ];

    const mockEvents: FamilyEvent[] = [
      {
        id: 'dinner',
        title: 'Family Dinner',
        type: 'gathering',
        date: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
        participants: ['mom', 'dad', 'sister'],
        location: 'Home',
        reminders: [30, 15],
        isRecurring: true,
        priority: 'medium',
      },
      {
        id: 'birthday',
        title: "Sarah's Birthday",
        type: 'birthday',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
        participants: ['mom', 'dad'],
        reminders: [1440, 60], // 1 day and 1 hour before
        isRecurring: true,
        priority: 'high',
      },
    ];

    setFamilyMembers(mockMembers);
    setUpcomingEvents(mockEvents);
  };

  const setupSynergyConnections = () => {
    // Connect with social module for family communications
    synergyEngine.addConnection({
      sourceModule: 'social',
      targetModule: 'family',
      dataType: 'message',
      transformFn: (data) => ({
        type: 'family_message',
        sender: data.senderName,
        content: data.content,
        timestamp: data.timestamp,
      }),
      conditions: (data) => data.channelType === 'family',
    });

    // Connect with finance module for family expenses
    synergyEngine.addConnection({
      sourceModule: 'finance',
      targetModule: 'family',
      dataType: 'expense',
      transformFn: (data) => ({
        type: 'family_expense',
        amount: data.amount,
        category: data.category,
        paidBy: data.paidBy,
        beneficiaries: data.beneficiaries,
      }),
      conditions: (data) => data.category === 'family' || data.beneficiaries?.length > 1,
    });

    // Connect with entertainment for family activities
    synergyEngine.addConnection({
      sourceModule: 'entertainment',
      targetModule: 'family',
      dataType: 'content_selected',
      transformFn: (data) => ({
        type: 'family_activity_suggestion',
        contentType: data.contentType,
        genre: data.genre,
        platform: data.platform,
      }),
      conditions: (data) => data.genre?.includes('Family') || data.mood?.includes('Together'),
    });

    // Subscribe to family-related updates
    synergyEngine.subscribe('family', (data) => {
      handleSynergyUpdate(data);
    });
  };

  const handleSynergyUpdate = (data: any) => {
    switch (data.type) {
      case 'family_message':
        // Update family communication status
        updateMemberActivity(data.data.sender, 'messaging');
        break;
      case 'family_expense':
        // Add to shared resources
        addSharedResource({
          type: 'expense',
          title: `${data.data.category} expense`,
          content: data.data,
          sharedBy: data.data.paidBy,
          tags: ['expense', 'family'],
        });
        break;
      case 'location_update':
        // Update member location
        updateMemberLocation(data.data.memberId, data.data.location);
        break;
    }
  };

  const startLocationTracking = async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') return;

    // Track location for family sharing (with privacy controls)
    Location.watchPositionAsync(
      {
        accuracy: Location.Accuracy.Balanced,
        timeInterval: 300000, // 5 minutes
        distanceInterval: 100, // 100 meters
      },
      (location) => {
        synergyEngine.shareData({
          moduleId: 'family',
          type: 'location_update',
          data: {
            memberId: 'user',
            location: {
              lat: location.coords.latitude,
              lng: location.coords.longitude,
              timestamp: Date.now(),
            },
          },
          tags: ['location', 'family', 'tracking'],
          privacy: 'family',
        });
      }
    );
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const updateMemberActivity = (memberId: string, activity: string) => {
    setFamilyMembers(prev => 
      prev.map(member => 
        member.id === memberId 
          ? { ...member, currentActivity: activity }
          : member
      )
    );
  };

  const updateMemberLocation = (memberId: string, location: any) => {
    setFamilyMembers(prev => 
      prev.map(member => 
        member.id === memberId 
          ? { ...member, location }
          : member
      )
    );
  };

  const addSharedResource = (resource: Omit<SharedResource, 'id' | 'timestamp' | 'sharedWith'>) => {
    const newResource: SharedResource = {
      ...resource,
      id: `resource_${Date.now()}`,
      timestamp: Date.now(),
      sharedWith: familyMembers.map(m => m.id),
    };

    setSharedResources(prev => [newResource, ...prev]);
  };

  const renderFamilyOverview = () => (
    <ScrollView style={{ flex: 1, padding: 16 }}>
      {/* Family Status Ring */}
      <View style={{ alignItems: 'center', marginBottom: 24 }}>
        <Animated.View
          style={{
            transform: [{ scale: pulseAnimation }],
          }}
        >
          <LinearGradient
            colors={['#FF6B6B', '#FF8E8E']}
            style={{
              width: 120,
              height: 120,
              borderRadius: 60,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Text style={{ color: 'white', fontSize: 24, fontWeight: 'bold' }}>
              {familyMembers.filter(m => m.status === 'online').length}
            </Text>
            <Text style={{ color: 'white', fontSize: 12 }}>
              Online
            </Text>
          </LinearGradient>
        </Animated.View>
        
        <Text style={{ color: 'white', fontSize: 18, fontWeight: 'bold', marginTop: 8 }}>
          Family Status
        </Text>
      </View>

      {/* Quick Family Members */}
      <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold', marginBottom: 12 }}>
        Family Members
      </Text>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {familyMembers.map((member) => (
          <TouchableOpacity
            key={member.id}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              // Navigate to member details
            }}
            style={{ marginRight: 12 }}
          >
            <BlurView
              intensity={20}
              style={{
                width: 80,
                padding: 12,
                borderRadius: 16,
                alignItems: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              }}
            >
              <LinearGradient
                colors={getStatusColors(member.status)}
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 8,
                }}
              >
                <Text style={{ color: 'white', fontWeight: 'bold' }}>
                  {member.name.charAt(0)}
                </Text>
              </LinearGradient>
              
              <Text style={{ color: 'white', fontSize: 10, textAlign: 'center' }}>
                {member.name}
              </Text>
              
              <Text style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: 8, textAlign: 'center' }}>
                {member.currentActivity}
              </Text>
            </BlurView>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Upcoming Events */}
      <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold', marginTop: 24, marginBottom: 12 }}>
        Upcoming Events
      </Text>
      
      {upcomingEvents.slice(0, 3).map((event) => (
        <BlurView
          key={event.id}
          intensity={20}
          style={{
            marginBottom: 12,
            padding: 16,
            borderRadius: 16,
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
          }}
        >
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <View style={{ flex: 1 }}>
              <Text style={{ color: 'white', fontSize: 14, fontWeight: 'bold' }}>
                {event.title}
              </Text>
              <Text style={{ color: 'rgba(255, 255, 255, 0.7)', fontSize: 12 }}>
                {event.date.toLocaleDateString()} at {event.date.toLocaleTimeString()}
              </Text>
              {event.location && (
                <Text style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: 10 }}>
                  📍 {event.location}
                </Text>
              )}
            </View>
            
            <LinearGradient
              colors={getPriorityColors(event.priority)}
              style={{
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 8,
              }}
            >
              <Text style={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>
                {event.type.toUpperCase()}
              </Text>
            </LinearGradient>
          </View>
        </BlurView>
      ))}

      {/* Recent Shared Items */}
      <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold', marginTop: 24, marginBottom: 12 }}>
        Recently Shared
      </Text>
      
      {sharedResources.slice(0, 3).map((resource) => (
        <BlurView
          key={resource.id}
          intensity={20}
          style={{
            marginBottom: 12,
            padding: 16,
            borderRadius: 16,
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={{ fontSize: 20, marginRight: 12 }}>
              {getResourceIcon(resource.type)}
            </Text>
            
            <View style={{ flex: 1 }}>
              <Text style={{ color: 'white', fontSize: 14, fontWeight: 'bold' }}>
                {resource.title}
              </Text>
              <Text style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: 12 }}>
                Shared by {resource.sharedBy}
              </Text>
            </View>
          </View>
        </BlurView>
      ))}
    </ScrollView>
  );

  const getStatusColors = (status: string): [string, string] => {
    switch (status) {
      case 'online': return ['#4CAF50', '#66BB6A'];
      case 'busy': return ['#FF9800', '#FFB74D'];
      case 'away': return ['#FFC107', '#FFCA28'];
      case 'offline': return ['#757575', '#9E9E9E'];
      default: return ['#667eea', '#764ba2'];
    }
  };

  const getPriorityColors = (priority: string): [string, string] => {
    switch (priority) {
      case 'urgent': return ['#F44336', '#EF5350'];
      case 'high': return ['#FF9800', '#FFB74D'];
      case 'medium': return ['#2196F3', '#42A5F5'];
      case 'low': return ['#4CAF50', '#66BB6A'];
      default: return ['#667eea', '#764ba2'];
    }
  };

  const getResourceIcon = (type: string): string => {
    switch (type) {
      case 'photo': return '📷';
      case 'video': return '🎥';
      case 'document': return '📄';
      case 'location': return '📍';
      case 'expense': return '💰';
      case 'memory': return '💭';
      default: return '📱';
    }
  };

  const renderHeader = () => (
    <BlurView
      intensity={80}
      style={{
        paddingHorizontal: 20,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(255, 255, 255, 0.1)',
        backgroundColor: 'rgba(0, 0, 0, 0.2)',
      }}
    >
      <Text style={{
        color: 'white',
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
      }}>
        Family Hub
      </Text>
      <Text style={{
        color: 'rgba(255, 255, 255, 0.7)',
        fontSize: 14,
        textAlign: 'center',
        marginTop: 4,
      }}>
        Stay connected with your loved ones
      </Text>
    </BlurView>
  );

  return (
    <LinearGradient
      colors={['#FF6B6B', '#FF8E8E']}
      style={{ flex: 1 }}
    >
      {renderHeader()}
      <View style={{ flex: 1 }}>
        {renderFamilyOverview()}
      </View>
    </LinearGradient>
  );
};

export default FamilyHub;
