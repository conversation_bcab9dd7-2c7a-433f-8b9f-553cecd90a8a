#!/bin/bash

echo "🚀 Starting SuperApp..."
echo ""

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

echo "🌐 Starting Expo development server..."
echo ""
echo "Choose your platform:"
echo "1) Web Browser (recommended for testing)"
echo "2) iOS Simulator"
echo "3) Android Emulator"
echo "4) All platforms"
echo ""

read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        echo "🌐 Opening in web browser..."
        npx expo start --web
        ;;
    2)
        echo "📱 Opening iOS simulator..."
        npx expo start --ios
        ;;
    3)
        echo "🤖 Opening Android emulator..."
        npx expo start --android
        ;;
    4)
        echo "🚀 Starting all platforms..."
        npx expo start
        ;;
    *)
        echo "🌐 Default: Opening in web browser..."
        npx expo start --web
        ;;
esac
