{"name": "superapp", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^15.0.0", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/drawer": "^7.5.7", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.7", "@reduxjs/toolkit": "^2.8.2", "@types/react-native-vector-icons": "^6.4.18", "expo": "~53.0.22", "expo-av": "^15.1.7", "expo-blur": "^14.1.5", "expo-camera": "^16.1.11", "expo-haptics": "^14.1.4", "expo-linear-gradient": "^14.1.5", "expo-location": "^18.1.6", "expo-notifications": "^0.31.4", "expo-sensors": "^14.1.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.6", "react-native-gesture-handler": "^2.28.0", "react-native-reanimated": "^4.0.2", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.15.3", "react-native-svg": "^15.12.1", "react-native-vector-icons": "^10.3.0", "react-native-worklets": "^0.4.2", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}