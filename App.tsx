import React, { useState, useEffect } from 'react';
import { View, StatusBar, Dimensions, Text, SafeAreaView, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import * as Location from 'expo-location';
import * as Sensors from 'expo-sensors';

// Core Components
import SynergyEngine, { ModuleContext } from './src/core/SynergyEngine';
import AdaptiveNavigation, { NavigationModule } from './src/navigation/AdaptiveNavigation';
import FloatingWidget, { WidgetData } from './src/components/innovative/FloatingWidget';

// Modules
import UnifiedCommunications from './src/modules/social/UnifiedCommunications';
import EntertainmentSpectrum from './src/modules/entertainment/EntertainmentSpectrum';
import FamilyHub from './src/modules/family/FamilyHub';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function App() {
  const [currentModule, setCurrentModule] = useState<string>('social');
  const [widgets, setWidgets] = useState<WidgetData[]>([]);
  const [context, setContext] = useState<ModuleContext | null>(null);

  const synergyEngine = SynergyEngine.getInstance();

  useEffect(() => {
    initializeApp();
    setupContextTracking();
  }, []);

  const initializeApp = async () => {
    // Initialize synergy engine with user context
    const initialContext: ModuleContext = {
      userId: 'user_123',
      timeOfDay: getTimeOfDay(),
      preferences: {
        theme: 'dark',
        notifications: true,
        locationSharing: true,
      },
    };

    synergyEngine.setContext(initialContext);
    setContext(initialContext);

    // Setup initial widgets
    createWelcomeWidget();
  };

  const setupContextTracking = async () => {
    // Location tracking
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status === 'granted') {
      const location = await Location.getCurrentPositionAsync({});
      updateContext({
        location: {
          lat: location.coords.latitude,
          lng: location.coords.longitude,
        },
      });
    }

    // Activity detection using device sensors
    Sensors.Accelerometer.addListener(({ x, y, z }) => {
      const magnitude = Math.sqrt(x * x + y * y + z * z);
      const activity = magnitude > 15 ? 'walking' : magnitude > 5 ? 'moving' : 'stationary';

      if (context?.activity !== activity) {
        updateContext({ activity });
      }
    });

    Sensors.Accelerometer.setUpdateInterval(5000); // Update every 5 seconds
  };

  const updateContext = (updates: Partial<ModuleContext>) => {
    if (!context) return;

    const newContext = { ...context, ...updates };
    synergyEngine.setContext(newContext);
    setContext(newContext);
  };

  const getTimeOfDay = (): 'morning' | 'afternoon' | 'evening' | 'night' => {
    const hour = new Date().getHours();
    if (hour < 6) return 'night';
    if (hour < 12) return 'morning';
    if (hour < 18) return 'afternoon';
    if (hour < 22) return 'evening';
    return 'night';
  };

  const createWelcomeWidget = () => {
    const welcomeWidget: WidgetData = {
      id: 'welcome',
      type: 'info',
      title: 'Welcome to SuperApp!',
      content: (
        <View>
          <Text style={{ color: 'white', fontSize: 12 }}>
            Your unified life management platform is ready.
          </Text>
        </View>
      ),
      priority: 'medium',
      moduleId: 'system',
      autoHide: true,
      hideAfter: 5000,
      actions: [
        {
          label: 'Explore',
          onPress: () => {
            dismissWidget('welcome');
            // Could trigger a tour or highlight features
          },
        },
      ],
    };

    setWidgets([welcomeWidget]);
  };

  const dismissWidget = (widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
  };

  const expandWidget = (widgetId: string) => {
    // Handle widget expansion - could navigate to relevant module
    const widget = widgets.find(w => w.id === widgetId);
    if (widget) {
      setCurrentModule(widget.moduleId);
    }
  };

  const navigationModules: NavigationModule[] = [
    {
      id: 'social',
      name: 'Social',
      icon: '💬',
      color: ['#667eea', '#764ba2'],
      priority: 1,
      contextRelevance: (ctx) => {
        if (ctx.activity === 'stationary') return 0.8;
        if (ctx.timeOfDay === 'evening') return 0.9;
        return 0.6;
      },
      component: UnifiedCommunications,
    },
    {
      id: 'entertainment',
      name: 'Entertainment',
      icon: '🎬',
      color: ['#FF6B6B', '#FF8E8E'],
      priority: 2,
      contextRelevance: (ctx) => {
        if (ctx.timeOfDay === 'evening') return 0.9;
        if (ctx.activity === 'stationary') return 0.7;
        return 0.5;
      },
      component: EntertainmentSpectrum,
    },
    {
      id: 'family',
      name: 'Family',
      icon: '👨‍👩‍👧‍👦',
      color: ['#4ECDC4', '#44A08D'],
      priority: 3,
      contextRelevance: (ctx) => {
        if (ctx.timeOfDay === 'morning' || ctx.timeOfDay === 'evening') return 0.8;
        return 0.6;
      },
      component: FamilyHub,
    },
    // Additional modules would be added here
  ];

  const renderCurrentModule = () => {
    const module = navigationModules.find(m => m.id === currentModule);
    if (!module) return null;

    const ModuleComponent = module.component;
    return <ModuleComponent />;
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ExpoStatusBar style="light" backgroundColor="transparent" translucent />

      <SafeAreaView style={{ flex: 1, backgroundColor: '#000' }}>
        <View style={{ flex: 1 }}>
          {renderCurrentModule()}

          <AdaptiveNavigation
            modules={navigationModules}
            onModuleSelect={setCurrentModule}
            currentModule={currentModule}
          />

          {/* Floating Widgets */}
          {widgets.map((widget, index) => (
            <FloatingWidget
              key={widget.id}
              widget={widget}
              onDismiss={dismissWidget}
              onExpand={expandWidget}
              initialPosition={{
                x: screenWidth - 100,
                y: 200 + (index * 80),
              }}
            />
          ))}
        </View>
      </SafeAreaView>
    </GestureHandlerRootView>
  );
}
